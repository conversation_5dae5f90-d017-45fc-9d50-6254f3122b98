# Copyright 2009 The RE2 Authors.  All Rights Reserved.
# Use of this source code is governed by a BSD-style
# license that can be found in the LICENSE file.

# Bazel (http://bazel.build/) MODULE file for RE2.

module(
    name = "re2",
    version = "2024-07-02",
    compatibility_level = 1,
)

bazel_dep(name = "platforms", version = "0.0.10")
bazel_dep(name = "apple_support", version = "1.15.1")
bazel_dep(name = "rules_cc", version = "0.0.13")
bazel_dep(name = "abseil-cpp", version = "20240116.2")
bazel_dep(name = "rules_python", version = "0.33.2")
bazel_dep(name = "pybind11_bazel", version = "2.12.0")

# This is a temporary hack for `x64_x86_windows`.
# TODO(junyer): Remove whenever no longer needed.
cc_configure = use_extension("@rules_cc//cc:extensions.bzl", "cc_configure_extension")
use_repo(cc_configure, "local_config_cc")

# These dependencies will be ignored when the `re2` module is not
# the root module (or when `--ignore_dev_dependency` is enabled).
bazel_dep(name = "google_benchmark", version = "1.8.4", dev_dependency = True)
bazel_dep(name = "googletest", version = "1.14.0.bcr.1", dev_dependency = True)
bazel_dep(name = "abseil-py", version = "2.1.0", dev_dependency = True)
