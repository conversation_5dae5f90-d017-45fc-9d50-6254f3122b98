name: Pages
on:
  workflow_dispatch:
permissions:
  contents: read
jobs:
  build:
    runs-on: ubuntu-latest
    container:
      image: emscripten/emsdk
      # Don't run as root within the container.
      # Neither <PERSON><PERSON> nor <PERSON><PERSON> appreciates that.
      # 1001 is the GitHub Actions runner user.
      options: --init --user 1001
    env:
      BAZELISK_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      # <PERSON><PERSON> fails if the username is unknown.
      USER: runner
    steps:
      - uses: actions/checkout@v4.1.7
      - uses: bazel-contrib/setup-bazel@0.8.5
        with:
          bazelisk-version: '1.x'
      - run: app/build.sh
        shell: bash
      - uses: actions/upload-pages-artifact@v3.0.1
        with:
          path: app/deploy
  deploy:
    needs:
      - build
    permissions:
      contents: read
      # Needed for Pages deployment.
      id-token: write
      pages: write
    environment: github-pages
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.1.7
      - uses: actions/deploy-pages@v4.0.5
