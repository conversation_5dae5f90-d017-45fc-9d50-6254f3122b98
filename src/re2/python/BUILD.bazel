# Copyright 2009 The RE2 Authors.  All Rights Reserved.
# Use of this source code is governed by a BSD-style
# license that can be found in the LICENSE file.

# Bazel (http://bazel.build/) BUILD file for RE2 Python.

load("@pybind11_bazel//:build_defs.bzl", "pybind_extension")
load("@rules_python//python:defs.bzl", "py_library", "py_test")

pybind_extension(
    name = "_re2",
    srcs = ["_re2.cc"],
    deps = [
        "//:re2",
        "@abseil-cpp//absl/strings",
    ],
)

py_library(
    name = "re2",
    srcs = ["re2.py"],
    data = [":_re2"],
    imports = ["."],
    visibility = ["//visibility:public"],
)

py_test(
    name = "re2_test",
    size = "small",
    srcs = ["re2_test.py"],
    deps = [
        ":re2",
        "@abseil-py//absl/testing:absltest",
        "@abseil-py//absl/testing:parameterized",
    ],
)

# These are implementation details for `setup.py`, so they can be
# named however we want. For now, they are named to be consistent
# with the `--cpu` flag values that they will eventually replace.

platform(
    name = "darwin_x86_64",
    constraint_values = [
        "@platforms//cpu:x86_64",
        "@platforms//os:macos",
    ],
)

platform(
    name = "darwin_arm64",
    constraint_values = [
        "@platforms//cpu:arm64",
        "@platforms//os:macos",
    ],
)

platform(
    name = "x64_x86_windows",
    constraint_values = [
        "@platforms//cpu:x86_32",
        "@platforms//os:windows",
    ],
)

platform(
    name = "x64_windows",
    constraint_values = [
        "@platforms//cpu:x86_64",
        "@platforms//os:windows",
    ],
)
