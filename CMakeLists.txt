cmake_minimum_required(VERSION 3.15...4.0)

# Set the C++ standard
if (NOT DEFINED CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 23)
endif()

# Disable compiler extensions for better portability
set(CMAKE_CXX_EXTENSIONS OFF)

# Export compile commands for IDE integration
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Create symlink to compile_commands.json in project root for clangd
if(CMAKE_EXPORT_COMPILE_COMMANDS)
  add_custom_target(
    symlink_compile_commands ALL
    COMMAND ${CMAKE_COMMAND} -E create_symlink
      ${CMAKE_BINARY_DIR}/compile_commands.json
      ${CMAKE_SOURCE_DIR}/compile_commands.json
    DEPENDS ${CMAKE_BINARY_DIR}/compile_commands.json
    COMMENT "Creating symlink to compile_commands.json in project root"
  )
endif()

# Set the project name and language
# TODO: Change 'MyProject' to your actual project name
project(
  MyProject
  VERSION 0.1.0
  DESCRIPTION "A modern C++23 project"
  LANGUAGES CXX
)

# Prevent in-source builds
if(CMAKE_SOURCE_DIR STREQUAL CMAKE_BINARY_DIR)
  message(FATAL_ERROR "In-source builds are not allowed. Please create a separate build directory.")
endif()

# Set default build type to Release if not specified
if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
  set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the type of build." FORCE)
  set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Create interface libraries for project options and warnings
# TODO: These will be automatically renamed when you change the project name above
add_library(${PROJECT_NAME}_options INTERFACE)
add_library(${PROJECT_NAME}_warnings INTERFACE)

# Comprehensive compiler warnings configuration
if(MSVC)
  # MSVC warnings
  target_compile_options(${PROJECT_NAME}_warnings INTERFACE
    /permissive-  # Enforces standards conformance
    /W4           # All reasonable warnings
    /w14640       # Enable warning on thread unsafe static member initialization
    /w14242       # 'identifier': conversion from 'type1' to 'type1', possible loss of data
    /w14254       # 'operator': conversion from 'type1:field_bits' to 'type2:field_bits', possible loss of data
    /w14263       # 'function': member function does not override any base class virtual member function
    /w14265       # 'classname': class has virtual functions, but destructor is not virtual
    /w14287       # 'operator': unsigned/negative constant mismatch
    /we4289       # nonstandard extension used: 'variable': loop control variable declared in the for-loop is used outside the for-loop scope
    /w14296       # 'operator': expression is always 'boolean_value'
    /w14311       # 'variable': pointer truncation from 'type1' to 'type2'
    /w14545       # expression before comma evaluates to a function which is missing an argument list
    /w14546       # function call before comma missing argument list
    /w14547       # 'operator': operator before comma has no effect; expected operator with side-effect
    /w14549       # 'operator': operator before comma has no effect; did you intend 'operator'?
    /w14555       # expression has no effect; expected expression with side-effect
    /w14619       # pragma warning: there is no warning number 'number'
    /w14826       # Conversion from 'type1' to 'type_2' is sign-extended. This may cause unexpected runtime behavior
    /w14905       # wide string literal cast to 'LPSTR'
    /w14906       # string literal cast to 'LPWSTR'
    /w14928       # illegal copy-initialization; more than one user-defined conversion has been implicitly applied
  )
else()
  # GCC/Clang warnings
  target_compile_options(${PROJECT_NAME}_warnings INTERFACE
    # Core warnings (always recommended)
    -Wall
    -Wextra
    -Wshadow
    -Wnon-virtual-dtor
    -pedantic
    -Wold-style-cast
    -Wcast-align
    -Wunused
    -Woverloaded-virtual
    -Wpedantic
    -Wconversion
    -Wsign-conversion
    -Wdouble-promotion
    -Wformat=2

    # GCC-specific warnings (applied when using GCC)
    $<$<CXX_COMPILER_ID:GNU>:
      $<$<VERSION_GREATER_EQUAL:$<CXX_COMPILER_VERSION>,6.0>:
        -Wmisleading-indentation
        -Wduplicated-cond
        -Wnull-dereference
      >
      $<$<VERSION_GREATER_EQUAL:$<CXX_COMPILER_VERSION>,7.0>:
        -Wduplicated-branches
      >
      $<$<VERSION_GREATER_EQUAL:$<CXX_COMPILER_VERSION>,4.8>:
        -Wuseless-cast
      >
      -Wlogical-op
    >

    # Clang-specific warnings
    $<$<CXX_COMPILER_ID:Clang>:
      # Note: -Wimplicit-fallthrough is included in -Wextra for GCC but needs explicit enabling for Clang
      -Wimplicit-fallthrough
    >
  )
endif()

# Set C++ standard for the options target
target_compile_features(${PROJECT_NAME}_options INTERFACE cxx_std_${CMAKE_CXX_STANDARD})

# Create aliases for easier usage
add_library(${PROJECT_NAME}::${PROJECT_NAME}_options ALIAS ${PROJECT_NAME}_options)
add_library(${PROJECT_NAME}::${PROJECT_NAME}_warnings ALIAS ${PROJECT_NAME}_warnings)

# Add include directory
target_include_directories(${PROJECT_NAME}_options INTERFACE
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

# Add the src directory
add_subdirectory(src)

# Only build tests if this is the top-level project
if(PROJECT_IS_TOP_LEVEL)
  # Enable testing
  include(CTest)
  if(BUILD_TESTING)
    add_subdirectory(test)
  endif()
endif()
